﻿#pragma checksum "..\..\..\..\Views\WatermarkToolView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "43C9CBBB80A32DBF687C1D1BBDB9B4BEE00D7651"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// WatermarkToolView
    /// </summary>
    public partial class WatermarkToolView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 37 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectImageButton;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedFileText;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WatermarkTextBox;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FontSizeSlider;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FontSizeText;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider OpacitySlider;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock OpacityText;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ColorComboBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PositionComboBox;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button PreviewButton;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveButton;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetButton;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ImageContainer;
        
        #line default
        #line hidden
        
        
        #line 146 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultTip;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ImageCanvas;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image PreviewImage;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WatermarkText;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\WatermarkToolView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageInfoText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/watermarktoolview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\WatermarkToolView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SelectImageButton = ((System.Windows.Controls.Button)(target));
            
            #line 40 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.SelectImageButton.Click += new System.Windows.RoutedEventHandler(this.SelectImageButton_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SelectedFileText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.WatermarkTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.FontSizeSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 64 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.FontSizeSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.FontSizeSlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.FontSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.OpacitySlider = ((System.Windows.Controls.Slider)(target));
            
            #line 74 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.OpacitySlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.OpacitySlider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.OpacityText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ColorComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 82 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.ColorComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ColorComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.PositionComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 94 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.PositionComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PositionComboBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PreviewButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.PreviewButton.Click += new System.Windows.RoutedEventHandler(this.PreviewButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.SaveButton = ((System.Windows.Controls.Button)(target));
            
            #line 113 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.SaveButton.Click += new System.Windows.RoutedEventHandler(this.SaveButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ResetButton = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\WatermarkToolView.xaml"
            this.ResetButton.Click += new System.Windows.RoutedEventHandler(this.ResetButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ImageContainer = ((System.Windows.Controls.Grid)(target));
            return;
            case 14:
            this.DefaultTip = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.ImageCanvas = ((System.Windows.Controls.Canvas)(target));
            return;
            case 16:
            this.PreviewImage = ((System.Windows.Controls.Image)(target));
            return;
            case 17:
            this.WatermarkText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.ImageInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

