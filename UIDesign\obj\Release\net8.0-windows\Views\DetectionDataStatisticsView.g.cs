﻿#pragma checksum "..\..\..\..\Views\DetectionDataStatisticsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4594F646F5E8A390F6C7110BC588CB82C27772DD"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// DetectionDataStatisticsView
    /// </summary>
    public partial class DetectionDataStatisticsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 26 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProductModelComboBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DetectionResultComboBox;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox DetectionProcessComboBox;
        
        #line default
        #line hidden
        
        
        #line 153 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CameraSelectionComboBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProductCodeTextBox;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecordCountText;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageViewerTitle;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageViewerSubtitle;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Image MainImageDisplay;
        
        #line default
        #line hidden
        
        
        #line 423 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel DefaultImagePanel;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ImageToolbar;
        
        #line default
        #line hidden
        
        
        #line 460 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CameraInfoText;
        
        #line default
        #line hidden
        
        
        #line 464 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProductCodeInfoText;
        
        #line default
        #line hidden
        
        
        #line 468 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DetectionTimeInfoText;
        
        #line default
        #line hidden
        
        
        #line 472 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImageSizeInfoText;
        
        #line default
        #line hidden
        
        
        #line 482 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MeasurementValueText;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StandardValueText;
        
        #line default
        #line hidden
        
        
        #line 490 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DeviationValueText;
        
        #line default
        #line hidden
        
        
        #line 494 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock JudgmentResultText;
        
        #line default
        #line hidden
        
        
        #line 584 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StatsStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 587 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StatsEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 593 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatsDimensionComboBox;
        
        #line default
        #line hidden
        
        
        #line 610 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatsProductModelComboBox;
        
        #line default
        #line hidden
        
        
        #line 620 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatsProcessComboBox;
        
        #line default
        #line hidden
        
        
        #line 903 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker QualityStartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 906 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker QualityEndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 912 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QualityDimensionComboBox;
        
        #line default
        #line hidden
        
        
        #line 922 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QualityMetricComboBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/detectiondatastatisticsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 2:
            this.StartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.EndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.ProductModelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.DetectionResultComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            
            #line 135 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QueryData_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 138 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetQuery_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.DetectionProcessComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.CameraSelectionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 10:
            this.ProductCodeTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            
            #line 172 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.RecordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            
            #line 262 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 265 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 268 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 303 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 306 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 309 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 312 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 344 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 347 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 350 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 353 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ShowCameraImage_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.ImageViewerTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.ImageViewerSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.MainImageDisplay = ((System.Windows.Controls.Image)(target));
            return;
            case 27:
            this.DefaultImagePanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            this.ImageToolbar = ((System.Windows.Controls.Border)(target));
            return;
            case 29:
            this.CameraInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.ProductCodeInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.DetectionTimeInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.ImageSizeInfoText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.MeasurementValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.StandardValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.DeviationValueText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 36:
            this.JudgmentResultText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 37:
            this.StatsStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 38:
            this.StatsEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 39:
            this.StatsDimensionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 40:
            
            #line 605 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GenerateStats_Click);
            
            #line default
            #line hidden
            return;
            case 41:
            this.StatsProductModelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 42:
            this.StatsProcessComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 43:
            
            #line 631 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportStats_Click);
            
            #line default
            #line hidden
            return;
            case 44:
            
            #line 846 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportStatsReport_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            
            #line 849 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportCharts_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 852 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportDetailData_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 855 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.GeneratePDFReport_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.QualityStartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 49:
            this.QualityEndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 50:
            this.QualityDimensionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 51:
            this.QualityMetricComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 52:
            
            #line 934 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AnalyzeQuality_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            
            #line 1097 "..\..\..\..\Views\DetectionDataStatisticsView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportQualityReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

