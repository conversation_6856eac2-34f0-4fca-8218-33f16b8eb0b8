﻿#pragma checksum "..\..\..\..\Views\PermissionManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "0D32451075BE87FB0153254384CCB388A37E4726"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// PermissionManagementView
    /// </summary>
    public partial class PermissionManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\Views\PermissionManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid RoleDataGrid;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\PermissionManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddRoleButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\PermissionManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditRoleButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\PermissionManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteRoleButton;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Views\PermissionManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfigPermissionButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\PermissionManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnableDisableRoleButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/permissionmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PermissionManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RoleDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 36 "..\..\..\..\Views\PermissionManagementView.xaml"
            this.RoleDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.RoleDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddRoleButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\PermissionManagementView.xaml"
            this.AddRoleButton.Click += new System.Windows.RoutedEventHandler(this.AddRoleButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EditRoleButton = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\Views\PermissionManagementView.xaml"
            this.EditRoleButton.Click += new System.Windows.RoutedEventHandler(this.EditRoleButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteRoleButton = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\Views\PermissionManagementView.xaml"
            this.DeleteRoleButton.Click += new System.Windows.RoutedEventHandler(this.DeleteRoleButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ConfigPermissionButton = ((System.Windows.Controls.Button)(target));
            
            #line 92 "..\..\..\..\Views\PermissionManagementView.xaml"
            this.ConfigPermissionButton.Click += new System.Windows.RoutedEventHandler(this.ConfigPermissionButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.EnableDisableRoleButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\Views\PermissionManagementView.xaml"
            this.EnableDisableRoleButton.Click += new System.Windows.RoutedEventHandler(this.EnableDisableRoleButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

