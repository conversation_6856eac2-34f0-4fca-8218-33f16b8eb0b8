﻿#pragma checksum "..\..\..\..\Views\AddAlgorithmDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "63DFBBEFEB676B0BFFED079937A4E2FDA50438E9"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// AddAlgorithmDialog
    /// </summary>
    public partial class AddAlgorithmDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 44 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlgorithmTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox InitialVersionTextBox;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AlgorithmFilePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameText;
        
        #line default
        #line hidden
        
        
        #line 121 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileModifiedText;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAlgorithmCheckBox;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoTestCheckBox;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BackupFileCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/addalgorithmdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AlgorithmNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.AlgorithmTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.InitialVersionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.AlgorithmDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.AlgorithmFilePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            
            #line 98 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseAlgorithmFile_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.FileNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.FileModifiedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.EnableAlgorithmCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.AutoTestCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 12:
            this.BackupFileCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 13:
            
            #line 162 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 165 "..\..\..\..\Views\AddAlgorithmDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

