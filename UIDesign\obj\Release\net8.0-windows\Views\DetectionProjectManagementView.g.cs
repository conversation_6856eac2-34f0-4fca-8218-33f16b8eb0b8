﻿#pragma checksum "..\..\..\..\Views\DetectionProjectManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3D6044F0E78792F20A6E8A5D12F079BE621BDFDF"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// DetectionProjectManagementView
    /// </summary>
    public partial class DetectionProjectManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ProjectDataGrid;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddProjectButton;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditProjectButton;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteProjectButton;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyProjectButton;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnableDisableButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/detectionprojectmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProjectDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 36 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            this.ProjectDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ProjectDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            this.AddProjectButton.Click += new System.Windows.RoutedEventHandler(this.AddProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EditProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            this.EditProjectButton.Click += new System.Windows.RoutedEventHandler(this.EditProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 89 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            this.DeleteProjectButton.Click += new System.Windows.RoutedEventHandler(this.DeleteProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CopyProjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 92 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            this.CopyProjectButton.Click += new System.Windows.RoutedEventHandler(this.CopyProjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.EnableDisableButton = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\Views\DetectionProjectManagementView.xaml"
            this.EnableDisableButton.Click += new System.Windows.RoutedEventHandler(this.EnableDisableButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

