﻿#pragma checksum "..\..\..\..\Views\DetectionTypeManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D6DF04CE63B6FF3EB01AF90D1A529141AFF0B658"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// DetectionTypeManagementView
    /// </summary>
    public partial class DetectionTypeManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid TypeDataGrid;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button AddTypeButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EditTypeButton;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeleteTypeButton;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyTypeButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EnableDisableTypeButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/detectiontypemanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TypeDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 36 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            this.TypeDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.TypeDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AddTypeButton = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            this.AddTypeButton.Click += new System.Windows.RoutedEventHandler(this.AddTypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.EditTypeButton = ((System.Windows.Controls.Button)(target));
            
            #line 85 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            this.EditTypeButton.Click += new System.Windows.RoutedEventHandler(this.EditTypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DeleteTypeButton = ((System.Windows.Controls.Button)(target));
            
            #line 88 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            this.DeleteTypeButton.Click += new System.Windows.RoutedEventHandler(this.DeleteTypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.CopyTypeButton = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            this.CopyTypeButton.Click += new System.Windows.RoutedEventHandler(this.CopyTypeButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.EnableDisableTypeButton = ((System.Windows.Controls.Button)(target));
            
            #line 94 "..\..\..\..\Views\DetectionTypeManagementView.xaml"
            this.EnableDisableTypeButton.Click += new System.Windows.RoutedEventHandler(this.EnableDisableTypeButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

