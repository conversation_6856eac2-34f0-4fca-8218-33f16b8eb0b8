using System;
using System.Collections.Generic;
using System.Drawing;

namespace CameraTechVerify.Interfaces
{
    /// <summary>
    /// 相机控制接口
    /// </summary>
    public interface ICameraController : IDisposable
    {
        #region 事件定义
        
        /// <summary>
        /// 图像接收事件
        /// </summary>
        event EventHandler<ImageReceivedEventArgs> ImageReceived;
        
        /// <summary>
        /// 连接状态改变事件
        /// </summary>
        event EventHandler<ConnectionStatusChangedEventArgs> ConnectionStatusChanged;
        
        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<ErrorOccurredEventArgs> ErrorOccurred;

        #endregion

        #region 基本属性

        /// <summary>
        /// 相机是否已连接
        /// </summary>
        bool IsConnected { get; }

        /// <summary>
        /// 相机是否正在采集
        /// </summary>
        bool IsGrabbing { get; }

        /// <summary>
        /// 相机设备信息
        /// </summary>
        CameraDeviceInfo DeviceInfo { get; }

        /// <summary>
        /// 相机参数配置
        /// </summary>
        CameraParameters Parameters { get; set; }

        #endregion

        #region 设备管理

        /// <summary>
        /// 枚举可用的相机设备
        /// </summary>
        /// <returns>相机设备列表</returns>
        List<CameraDeviceInfo> EnumerateDevices();

        /// <summary>
        /// 连接到指定的相机设备
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>连接是否成功</returns>
        bool Connect(CameraDeviceInfo deviceInfo);

        /// <summary>
        /// 通过索引连接相机
        /// </summary>
        /// <param name="deviceIndex">设备索引</param>
        /// <returns>连接是否成功</returns>
        bool Connect(int deviceIndex);

        /// <summary>
        /// 断开相机连接
        /// </summary>
        void Disconnect();

        #endregion

        #region 图像采集

        /// <summary>
        /// 开始连续采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool StartGrabbing();

        /// <summary>
        /// 停止连续采集
        /// </summary>
        /// <returns>操作是否成功</returns>
        bool StopGrabbing();

        /// <summary>
        /// 单次拍照
        /// </summary>
        /// <returns>拍摄的图像</returns>
        Bitmap CaptureImage();

        /// <summary>
        /// 异步单次拍照
        /// </summary>
        /// <returns>拍摄的图像任务</returns>
        System.Threading.Tasks.Task<Bitmap> CaptureImageAsync();

        #endregion

        #region 参数控制

        /// <summary>
        /// 设置曝光时间（微秒）
        /// </summary>
        /// <param name="exposureTime">曝光时间</param>
        /// <returns>设置是否成功</returns>
        bool SetExposureTime(float exposureTime);

        /// <summary>
        /// 获取曝光时间（微秒）
        /// </summary>
        /// <returns>当前曝光时间</returns>
        float GetExposureTime();

        /// <summary>
        /// 设置增益值
        /// </summary>
        /// <param name="gain">增益值</param>
        /// <returns>设置是否成功</returns>
        bool SetGain(float gain);

        /// <summary>
        /// 获取增益值
        /// </summary>
        /// <returns>当前增益值</returns>
        float GetGain();

        /// <summary>
        /// 设置图像分辨率
        /// </summary>
        /// <param name="width">图像宽度</param>
        /// <param name="height">图像高度</param>
        /// <returns>设置是否成功</returns>
        bool SetResolution(int width, int height);

        /// <summary>
        /// 获取图像分辨率
        /// </summary>
        /// <returns>当前分辨率</returns>
        Size GetResolution();

        /// <summary>
        /// 设置ROI区域
        /// </summary>
        /// <param name="x">X坐标</param>
        /// <param name="y">Y坐标</param>
        /// <param name="width">宽度</param>
        /// <param name="height">高度</param>
        /// <returns>设置是否成功</returns>
        bool SetROI(int x, int y, int width, int height);

        /// <summary>
        /// 获取ROI区域
        /// </summary>
        /// <returns>当前ROI区域</returns>
        Rectangle GetROI();

        /// <summary>
        /// 设置帧率
        /// </summary>
        /// <param name="frameRate">帧率</param>
        /// <returns>设置是否成功</returns>
        bool SetFrameRate(float frameRate);

        /// <summary>
        /// 获取帧率
        /// </summary>
        /// <returns>当前帧率</returns>
        float GetFrameRate();

        /// <summary>
        /// 设置触发模式
        /// </summary>
        /// <param name="triggerMode">触发模式</param>
        /// <returns>设置是否成功</returns>
        bool SetTriggerMode(TriggerMode triggerMode);

        /// <summary>
        /// 获取触发模式
        /// </summary>
        /// <returns>当前触发模式</returns>
        TriggerMode GetTriggerMode();

        /// <summary>
        /// 软件触发
        /// </summary>
        /// <returns>触发是否成功</returns>
        bool SoftwareTrigger();

        #endregion

        #region 高级功能

        /// <summary>
        /// 获取参数范围信息
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数范围信息</returns>
        ParameterRange GetParameterRange(string parameterName);

        /// <summary>
        /// 设置自定义参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <param name="value">参数值</param>
        /// <returns>设置是否成功</returns>
        bool SetParameter(string parameterName, object value);

        /// <summary>
        /// 获取自定义参数
        /// </summary>
        /// <param name="parameterName">参数名称</param>
        /// <returns>参数值</returns>
        object GetParameter(string parameterName);

        /// <summary>
        /// 保存参数配置到文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>保存是否成功</returns>
        bool SaveParametersToFile(string filePath);

        /// <summary>
        /// 从文件加载参数配置
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>加载是否成功</returns>
        bool LoadParametersFromFile(string filePath);

        /// <summary>
        /// 重置参数到默认值
        /// </summary>
        /// <returns>重置是否成功</returns>
        bool ResetParameters();

        #endregion
    }
}
