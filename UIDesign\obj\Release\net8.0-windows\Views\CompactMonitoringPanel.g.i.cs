﻿#pragma checksum "..\..\..\..\Views\CompactMonitoringPanel.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "91945284359EB6254518453205E4B6ABF4372040"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// CompactMonitoringPanel
    /// </summary>
    public partial class CompactMonitoringPanel : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 33 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RunTimeText;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalCountText;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PassCountText;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FailCountText;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PassRateText;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessSpeedText;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AvgTimeText;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UtilizationText;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/compactmonitoringpanel.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RunTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TotalCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.PassCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.FailCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.PassRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            
            #line 169 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PauseDetection_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 172 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StopDetection_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 175 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EmergencyStop_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 178 "..\..\..\..\Views\CompactMonitoringPanel.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ViewDetails_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ProcessSpeedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.AvgTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.UtilizationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

