﻿#pragma checksum "..\..\..\..\Views\AddProcessDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6877EBAF3183BAA2AFBA641A334320B575213038"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// AddProcessDialog
    /// </summary>
    public partial class AddProcessDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 44 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProcessNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProcessVersionTextBox;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProcessTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProcessDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableProcessCheckBox;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox UseTemplateCheckBox;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel TemplateSelectionPanel;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TemplateComboBox;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\AddProcessDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoConfigCheckBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;V1.0.0.0;component/views/addprocessdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AddProcessDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ProcessNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.ProcessVersionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.ProcessTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.ProcessDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.EnableProcessCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.UseTemplateCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 92 "..\..\..\..\Views\AddProcessDialog.xaml"
            this.UseTemplateCheckBox.Checked += new System.Windows.RoutedEventHandler(this.UseTemplate_Checked);
            
            #line default
            #line hidden
            
            #line 92 "..\..\..\..\Views\AddProcessDialog.xaml"
            this.UseTemplateCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.UseTemplate_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TemplateSelectionPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 8:
            this.TemplateComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.AutoConfigCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            
            #line 119 "..\..\..\..\Views\AddProcessDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateProcess_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 122 "..\..\..\..\Views\AddProcessDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.Cancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

