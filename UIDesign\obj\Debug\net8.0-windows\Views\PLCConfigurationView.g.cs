﻿#pragma checksum "..\..\..\..\Views\PLCConfigurationView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F5E774CC82F59D1AB0111666C98CD617BB15E0BA"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// PLCConfigurationView
    /// </summary>
    public partial class PLCConfigurationView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox PLCListBox;
        
        #line default
        #line hidden
        
        
        #line 253 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox PLCTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProtocolComboBox;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox IPAddressTextBox;
        
        #line default
        #line hidden
        
        
        #line 281 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox PortTextBox;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RackTextBox;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SlotTextBox;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScanCycleTextBox;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RetryCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 350 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ReadDBTextBox;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox WriteDBTextBox;
        
        #line default
        #line hidden
        
        
        #line 382 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ConnectionStatusIndicator;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectionStatusText;
        
        #line default
        #line hidden
        
        
        #line 385 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastConnectTimeText;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConnectButton;
        
        #line default
        #line hidden
        
        
        #line 395 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DisconnectButton;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestButton;
        
        #line default
        #line hidden
        
        
        #line 404 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SendCountText;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ReceiveCountText;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorCountText;
        
        #line default
        #line hidden
        
        
        #line 434 "..\..\..\..\Views\PLCConfigurationView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ConnectDurationText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/plcconfigurationview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PLCConfigurationView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.PLCListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 41 "..\..\..\..\Views\PLCConfigurationView.xaml"
            this.PLCListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PLCListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 200 "..\..\..\..\Views\PLCConfigurationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddPLC_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 203 "..\..\..\..\Views\PLCConfigurationView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeletePLC_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.PLCTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.ProtocolComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 6:
            this.IPAddressTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.PortTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.RackTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.SlotTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.ScanCycleTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.RetryCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 13:
            this.ReadDBTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.WriteDBTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ConnectionStatusIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 16:
            this.ConnectionStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.LastConnectTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.ConnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 393 "..\..\..\..\Views\PLCConfigurationView.xaml"
            this.ConnectButton.Click += new System.Windows.RoutedEventHandler(this.ConnectButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.DisconnectButton = ((System.Windows.Controls.Button)(target));
            
            #line 397 "..\..\..\..\Views\PLCConfigurationView.xaml"
            this.DisconnectButton.Click += new System.Windows.RoutedEventHandler(this.DisconnectButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TestButton = ((System.Windows.Controls.Button)(target));
            
            #line 401 "..\..\..\..\Views\PLCConfigurationView.xaml"
            this.TestButton.Click += new System.Windows.RoutedEventHandler(this.TestButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 406 "..\..\..\..\Views\PLCConfigurationView.xaml"
            this.SaveConfigButton.Click += new System.Windows.RoutedEventHandler(this.SaveConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.SendCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.ReceiveCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.ErrorCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.ConnectDurationText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

