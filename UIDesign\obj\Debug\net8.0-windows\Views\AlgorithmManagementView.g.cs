﻿#pragma checksum "..\..\..\..\Views\AlgorithmManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3961F71B1C60505F30AC12EB3A62AF834AF2049A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// AlgorithmManagementView
    /// </summary>
    public partial class AlgorithmManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 52 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox StatusFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AlgorithmListPanel;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AlgorithmNameText;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VersionText;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FilePathText;
        
        #line default
        #line hidden
        
        
        #line 200 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileSizeText;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ImportTimeText;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DescriptionText;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VersionHistoryComboBox;
        
        #line default
        #line hidden
        
        
        #line 304 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TestImagePathTextBox;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TestStatusText;
        
        #line default
        #line hidden
        
        
        #line 368 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ExecutionTimeText;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DetectionResultText;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\..\..\Views\AlgorithmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TestLogTextBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;V1.0.0.0;component/views/algorithmmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 45 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddNewAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.StatusFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.AlgorithmListPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            
            #line 71 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AlgorithmItem_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 85 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AlgorithmItem_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 99 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AlgorithmItem_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 113 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AlgorithmItem_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 127 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.AlgorithmItem_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.AlgorithmNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.VersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.FilePathText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.FileSizeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.ImportTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.DescriptionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.VersionHistoryComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 233 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            this.VersionHistoryComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.VersionHistory_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 242 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.UploadNewVersion_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 245 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.RollbackVersion_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 250 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteVersion_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 253 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CompareVersions_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 263 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.EnableAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 266 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DisableAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 269 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            
            #line 274 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            
            #line 277 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyAlgorithm_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.TestImagePathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 28:
            
            #line 309 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BrowseTestImage_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 345 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StartTest_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 348 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.StopTest_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 351 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearTestResult_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.TestStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.ExecutionTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 34:
            this.DetectionResultText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 35:
            this.TestLogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 36:
            
            #line 393 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveConfiguration_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            
            #line 396 "..\..\..\..\Views\AlgorithmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyChanges_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

