﻿#pragma checksum "..\..\..\..\Views\ParameterHistoryView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2DBABBC5C90FE8E2DCACFD57F13CA3F1B797F007"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// ParameterHistoryView
    /// </summary>
    public partial class ParameterHistoryView : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 23 "..\..\..\..\Views\ParameterHistoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ModelNameText;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\Views\ParameterHistoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox VersionListBox;
        
        #line default
        #line hidden
        
        
        #line 126 "..\..\..\..\Views\ParameterHistoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedVersionText;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\ParameterHistoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedTimeText;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\Views\ParameterHistoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ParameterDetailPanel;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\ParameterHistoryView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DefaultTip;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/parameterhistoryview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ParameterHistoryView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.ModelNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.VersionListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 52 "..\..\..\..\Views\ParameterHistoryView.xaml"
            this.VersionListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.VersionListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SelectedVersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SelectedTimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ParameterDetailPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.DefaultTip = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            
            #line 152 "..\..\..\..\Views\ParameterHistoryView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

