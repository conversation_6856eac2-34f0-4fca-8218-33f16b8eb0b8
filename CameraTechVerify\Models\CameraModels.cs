using System;
using System.Drawing;

namespace CameraTechVerify.Interfaces
{
    #region 枚举定义

    /// <summary>
    /// 触发模式
    /// </summary>
    public enum TriggerMode
    {
        /// <summary>
        /// 连续模式
        /// </summary>
        Continuous,
        
        /// <summary>
        /// 软件触发
        /// </summary>
        Software,
        
        /// <summary>
        /// 硬件触发
        /// </summary>
        Hardware
    }

    /// <summary>
    /// 相机连接状态
    /// </summary>
    public enum ConnectionStatus
    {
        /// <summary>
        /// 已断开
        /// </summary>
        Disconnected,
        
        /// <summary>
        /// 连接中
        /// </summary>
        Connecting,
        
        /// <summary>
        /// 已连接
        /// </summary>
        Connected,
        
        /// <summary>
        /// 连接失败
        /// </summary>
        Failed
    }

    /// <summary>
    /// 像素格式
    /// </summary>
    public enum PixelFormat
    {
        /// <summary>
        /// 单色8位
        /// </summary>
        Mono8,
        
        /// <summary>
        /// 单色10位
        /// </summary>
        Mono10,
        
        /// <summary>
        /// 单色12位
        /// </summary>
        Mono12,
        
        /// <summary>
        /// RGB8
        /// </summary>
        RGB8,
        
        /// <summary>
        /// BGR8
        /// </summary>
        BGR8,
        
        /// <summary>
        /// YUV422
        /// </summary>
        YUV422
    }

    #endregion

    #region 数据结构

    /// <summary>
    /// 相机设备信息
    /// </summary>
    public class CameraDeviceInfo
    {
        /// <summary>
        /// 设备索引
        /// </summary>
        public int DeviceIndex { get; set; }

        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }

        /// <summary>
        /// 设备型号
        /// </summary>
        public string ModelName { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        public string SerialNumber { get; set; }

        /// <summary>
        /// 制造商名称
        /// </summary>
        public string ManufacturerName { get; set; }

        /// <summary>
        /// 设备版本
        /// </summary>
        public string DeviceVersion { get; set; }

        /// <summary>
        /// IP地址（网络相机）
        /// </summary>
        public string IpAddress { get; set; }

        /// <summary>
        /// MAC地址
        /// </summary>
        public string MacAddress { get; set; }

        /// <summary>
        /// 连接类型
        /// </summary>
        public string ConnectionType { get; set; }

        /// <summary>
        /// 设备是否可访问
        /// </summary>
        public bool IsAccessible { get; set; }

        public override string ToString()
        {
            return $"{ModelName} ({SerialNumber})";
        }
    }

    /// <summary>
    /// 相机参数配置
    /// </summary>
    public class CameraParameters
    {
        /// <summary>
        /// 曝光时间（微秒）
        /// </summary>
        public float ExposureTime { get; set; } = 10000;

        /// <summary>
        /// 增益值
        /// </summary>
        public float Gain { get; set; } = 0;

        /// <summary>
        /// 图像宽度
        /// </summary>
        public int Width { get; set; } = 1920;

        /// <summary>
        /// 图像高度
        /// </summary>
        public int Height { get; set; } = 1080;

        /// <summary>
        /// ROI区域
        /// </summary>
        public Rectangle ROI { get; set; } = Rectangle.Empty;

        /// <summary>
        /// 帧率
        /// </summary>
        public float FrameRate { get; set; } = 30;

        /// <summary>
        /// 触发模式
        /// </summary>
        public TriggerMode TriggerMode { get; set; } = TriggerMode.Continuous;

        /// <summary>
        /// 像素格式
        /// </summary>
        public PixelFormat PixelFormat { get; set; } = PixelFormat.RGB8;

        /// <summary>
        /// 自动曝光
        /// </summary>
        public bool AutoExposure { get; set; } = false;

        /// <summary>
        /// 自动增益
        /// </summary>
        public bool AutoGain { get; set; } = false;

        /// <summary>
        /// 自动白平衡
        /// </summary>
        public bool AutoWhiteBalance { get; set; } = false;

        /// <summary>
        /// 伽马值
        /// </summary>
        public float Gamma { get; set; } = 1.0f;

        /// <summary>
        /// 亮度
        /// </summary>
        public int Brightness { get; set; } = 0;

        /// <summary>
        /// 对比度
        /// </summary>
        public int Contrast { get; set; } = 0;

        /// <summary>
        /// 饱和度
        /// </summary>
        public int Saturation { get; set; } = 0;

        /// <summary>
        /// 色调
        /// </summary>
        public int Hue { get; set; } = 0;

        /// <summary>
        /// 锐化
        /// </summary>
        public int Sharpness { get; set; } = 0;

        /// <summary>
        /// 降噪
        /// </summary>
        public int NoiseReduction { get; set; } = 0;
    }

    /// <summary>
    /// 参数范围信息
    /// </summary>
    public class ParameterRange
    {
        /// <summary>
        /// 最小值
        /// </summary>
        public double MinValue { get; set; }

        /// <summary>
        /// 最大值
        /// </summary>
        public double MaxValue { get; set; }

        /// <summary>
        /// 步长
        /// </summary>
        public double StepSize { get; set; }

        /// <summary>
        /// 默认值
        /// </summary>
        public double DefaultValue { get; set; }

        /// <summary>
        /// 当前值
        /// </summary>
        public double CurrentValue { get; set; }

        /// <summary>
        /// 参数单位
        /// </summary>
        public string Unit { get; set; }

        /// <summary>
        /// 参数描述
        /// </summary>
        public string Description { get; set; }
    }

    #endregion

    #region 事件参数

    /// <summary>
    /// 图像接收事件参数
    /// </summary>
    public class ImageReceivedEventArgs : EventArgs
    {
        /// <summary>
        /// 接收到的图像
        /// </summary>
        public Bitmap Image { get; set; }

        /// <summary>
        /// 图像序号
        /// </summary>
        public long ImageNumber { get; set; }

        /// <summary>
        /// 时间戳
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// 图像宽度
        /// </summary>
        public int Width { get; set; }

        /// <summary>
        /// 图像高度
        /// </summary>
        public int Height { get; set; }

        /// <summary>
        /// 像素格式
        /// </summary>
        public PixelFormat PixelFormat { get; set; }
    }

    /// <summary>
    /// 连接状态改变事件参数
    /// </summary>
    public class ConnectionStatusChangedEventArgs : EventArgs
    {
        /// <summary>
        /// 连接状态
        /// </summary>
        public ConnectionStatus Status { get; set; }

        /// <summary>
        /// 状态描述
        /// </summary>
        public string Message { get; set; }

        /// <summary>
        /// 设备信息
        /// </summary>
        public CameraDeviceInfo DeviceInfo { get; set; }
    }

    /// <summary>
    /// 错误发生事件参数
    /// </summary>
    public class ErrorOccurredEventArgs : EventArgs
    {
        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }

        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; set; }

        /// <summary>
        /// 错误发生时间
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    #endregion
}
