﻿#pragma checksum "..\..\..\..\Views\AlarmManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "B01E84EF4325C82A6A56D125A806AC2DE5E475C2"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// AlarmManagementView
    /// </summary>
    public partial class AlarmManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 26 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TabControl MainTabControl;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleConfigTitle;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RuleConfigSubtitle;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel RuleConfigPanel;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RuleTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox AlarmLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RuleEnabledCheckBox;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RuleDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 275 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox MonitorObjectComboBox;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CheckIntervalTextBox;
        
        #line default
        #line hidden
        
        
        #line 295 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox TriggerConditionComboBox;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TimeoutTextBox;
        
        #line default
        #line hidden
        
        
        #line 315 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox FailureCountTextBox;
        
        #line default
        #line hidden
        
        
        #line 323 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox RecoveryConditionComboBox;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox AdvancedConditionTextBox;
        
        #line default
        #line hidden
        
        
        #line 356 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SoundAlarmCheckBox;
        
        #line default
        #line hidden
        
        
        #line 361 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox PopupAlarmCheckBox;
        
        #line default
        #line hidden
        
        
        #line 366 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EmailAlarmCheckBox;
        
        #line default
        #line hidden
        
        
        #line 371 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox SMSAlarmCheckBox;
        
        #line default
        #line hidden
        
        
        #line 376 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox NotificationPersonsTextBox;
        
        #line default
        #line hidden
        
        
        #line 400 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoStopCheckBox;
        
        #line default
        #line hidden
        
        
        #line 405 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LogRecordCheckBox;
        
        #line default
        #line hidden
        
        
        #line 410 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ExecuteScriptCheckBox;
        
        #line default
        #line hidden
        
        
        #line 416 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ScriptPathTextBox;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox LinkageDescriptionTextBox;
        
        #line default
        #line hidden
        
        
        #line 502 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker StartDatePicker;
        
        #line default
        #line hidden
        
        
        #line 505 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker EndDatePicker;
        
        #line default
        #line hidden
        
        
        #line 511 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QueryAlarmLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 522 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QueryAlarmStatusComboBox;
        
        #line default
        #line hidden
        
        
        #line 542 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox QueryAlarmTypeComboBox;
        
        #line default
        #line hidden
        
        
        #line 554 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox KeywordTextBox;
        
        #line default
        #line hidden
        
        
        #line 683 "..\..\..\..\Views\AlarmManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RecordCountText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/alarmmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\AlarmManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.MainTabControl = ((System.Windows.Controls.TabControl)(target));
            return;
            case 2:
            
            #line 93 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 96 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 99 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 108 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 111 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 114 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 123 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 126 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 129 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 138 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 141 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 144 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 153 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            
            #line 156 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            
            #line 159 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SelectRule_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            
            #line 170 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.AddRule_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 173 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ImportRules_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.RuleConfigTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.RuleConfigSubtitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.RuleConfigPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 22:
            this.RuleNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 23:
            this.RuleTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 24:
            this.AlarmLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 25:
            this.RuleEnabledCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.RuleDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 27:
            this.MonitorObjectComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 28:
            this.CheckIntervalTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 29:
            this.TriggerConditionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 30:
            this.TimeoutTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 31:
            this.FailureCountTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 32:
            this.RecoveryConditionComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 33:
            this.AdvancedConditionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 34:
            this.SoundAlarmCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.PopupAlarmCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 36:
            this.EmailAlarmCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 37:
            this.SMSAlarmCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 38:
            this.NotificationPersonsTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 39:
            this.AutoStopCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 40:
            this.LogRecordCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 41:
            this.ExecuteScriptCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 42:
            this.ScriptPathTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 43:
            this.LinkageDescriptionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 44:
            
            #line 440 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.TestRule_Click);
            
            #line default
            #line hidden
            return;
            case 45:
            
            #line 443 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveRule_Click);
            
            #line default
            #line hidden
            return;
            case 46:
            
            #line 446 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteRule_Click);
            
            #line default
            #line hidden
            return;
            case 47:
            
            #line 449 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyRule_Click);
            
            #line default
            #line hidden
            return;
            case 48:
            this.StartDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 49:
            this.EndDatePicker = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 50:
            this.QueryAlarmLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 51:
            this.QueryAlarmStatusComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 52:
            
            #line 534 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.QueryAlarms_Click);
            
            #line default
            #line hidden
            return;
            case 53:
            
            #line 537 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetQuery_Click);
            
            #line default
            #line hidden
            return;
            case 54:
            this.QueryAlarmTypeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 55:
            this.KeywordTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 56:
            
            #line 562 "..\..\..\..\Views\AlarmManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 57:
            this.RecordCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

