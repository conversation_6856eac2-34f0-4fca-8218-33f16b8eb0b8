﻿#pragma checksum "..\..\..\..\Views\ProcessFlowManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "03C0BAB87B47674A5A0CCC133D1D73F75773E5C1"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace UIDesign.Views {
    
    
    /// <summary>
    /// ProcessFlowManagementView
    /// </summary>
    public partial class ProcessFlowManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox NodeTypeFilterComboBox;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel NodeLibraryPanel;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProcessNameTextBox;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ProcessVersionTextBox;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer ProcessCanvasScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Canvas ProcessDesignCanvas;
        
        #line default
        #line hidden
        
        
        #line 362 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedNodeNameText;
        
        #line default
        #line hidden
        
        
        #line 363 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SelectedNodeTypeText;
        
        #line default
        #line hidden
        
        
        #line 370 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PropertyConfigPanel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/UIDesign;component/views/processflowmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 47 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CreateNewNode_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.NodeTypeFilterComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 57 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            this.NodeTypeFilterComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.NodeTypeFilter_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.NodeLibraryPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 4:
            
            #line 77 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 88 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 103 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 114 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 125 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 140 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 150 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.NodeItem_MouseDown);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 200 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.NewProcess_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 203 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SaveProcess_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 206 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ExportProcess_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.ProcessNameTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.ProcessVersionTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 16:
            this.ProcessCanvasScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 17:
            this.ProcessDesignCanvas = ((System.Windows.Controls.Canvas)(target));
            
            #line 237 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            this.ProcessDesignCanvas.Drop += new System.Windows.DragEventHandler(this.ProcessCanvas_Drop);
            
            #line default
            #line hidden
            
            #line 237 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            this.ProcessDesignCanvas.DragOver += new System.Windows.DragEventHandler(this.ProcessCanvas_DragOver);
            
            #line default
            #line hidden
            return;
            case 18:
            
            #line 259 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.FlowNode_MouseDown);
            
            #line default
            #line hidden
            return;
            case 19:
            
            #line 267 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.FlowNode_MouseDown);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 280 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Shapes.Path)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.FlowNode_MouseDown);
            
            #line default
            #line hidden
            return;
            case 21:
            
            #line 286 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.FlowNode_MouseDown);
            
            #line default
            #line hidden
            return;
            case 22:
            
            #line 298 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.FlowNode_MouseDown);
            
            #line default
            #line hidden
            return;
            case 23:
            
            #line 334 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ValidateProcess_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            
            #line 337 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ClearCanvas_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.SelectedNodeNameText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.SelectedNodeTypeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.PropertyConfigPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            
            #line 476 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ApplyNodeConfig_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            
            #line 479 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetNodeConfig_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            
            #line 484 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.DeleteFlowNode_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            
            #line 487 "..\..\..\..\Views\ProcessFlowManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CopyFlowNode_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

